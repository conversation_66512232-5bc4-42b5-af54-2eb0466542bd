import request from '@/utils/request'

// 查询知识库文档列表
export function listDocument(query) {
  return request({
    url: '/knowledge/build/document/list',
    method: 'get',
    params: query
  })
}

// 根据文件夹ID查询文档列表
export function getDocumentsByFolder(folderId) {
  return request({
    url: '/knowledge/build/document/folder/' + folderId,
    method: 'get'
  })
}

// 搜索文档
export function searchDocuments(keyword) {
  return request({
    url: '/knowledge/build/document/search',
    method: 'get',
    params: { keyword }
  })
}

// 新增知识库文档
export function addDocument(data) {
  return request({
    url: '/knowledge/build/document',
    method: 'post',
    data: data
  })
}

// 删除知识库文档
export function delDocument(ids) {
  return request({
    url: '/knowledge/build/document/' + ids,
    method: 'delete'
  })
}

// 上传文档
export function uploadDocument(data) {
  return request({
    url: '/knowledge/build/document/upload',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 批量上传文档
export function batchUploadDocuments(data) {
  return request({
    url: '/knowledge/build/document/batch-upload',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 处理文档
export function processDocument(id) {
  return request({
    url: '/knowledge/build/document/process/' + id,
    method: 'post'
  })
}

// ==================== 知识库相关接口 ====================

// 创建知识库
export function createKnowledgeBase(data) {
  return request({
    url: '/knowledge/build/knowledge-base/create',
    method: 'post',
    data: data
  })
}

// 查询知识库列表
export function listKnowledgeBase(query) {
  return request({
    url: '/knowledge/build/knowledge-base/list',
    method: 'get',
    params: query
  })
}

// 查询知识库详细
export function getKnowledgeBase(id) {
  return request({
    url: '/knowledge/build/knowledge-base/' + id,
    method: 'get'
  })
}

// 删除知识库
export function delKnowledgeBase(ids) {
  return request({
    url: '/knowledge/build/knowledge-base/' + ids,
    method: 'delete'
  })
}

// 查询知识库的策略配置
export function getKnowledgeBaseStrategies(knowledgeBaseId) {
  return request({
    url: '/knowledge/build/knowledge-base/' + knowledgeBaseId + '/strategies',
    method: 'get'
  })
}

// 导出知识库文档
export function exportDocument(query) {
  return request({
    url: '/knowledge/build/document/export',
    method: 'post',
    params: query
  })
}

// ==================== 文件夹相关接口 ====================

// 查询知识库文件夹树结构
export function getFolderTree(knowledgeBaseId) {
  return request({
    url: '/knowledge/build/folder/tree',
    method: 'get',
    params: { knowledgeBaseId }
  })
}

// 查询知识库文件夹列表
export function listFolder(query) {
  return request({
    url: '/knowledge/build/folder/list',
    method: 'get',
    params: query
  })
}

// 查询知识库文件夹详细
export function getFolder(id) {
  return request({
    url: '/knowledge/build/folder/' + id,
    method: 'get'
  })
}

// 新增知识库文件夹
export function addFolder(data) {
  return request({
    url: '/knowledge/build/folder',
    method: 'post',
    data: data
  })
}

// 修改知识库文件夹
export function updateFolder(data) {
  return request({
    url: '/knowledge/build/folder',
    method: 'put',
    data: data
  })
}

// 删除知识库文件夹
export function delFolder(ids) {
  return request({
    url: '/knowledge/build/folder/' + ids,
    method: 'delete'
  })
}
