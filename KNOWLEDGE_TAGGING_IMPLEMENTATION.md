# 知识库智能标签功能实现

## 🎯 功能概述

为知识库系统实现了完整的智能标签生成功能，能够自动分析文档内容并生成相关标签，解决了您提到的"航天文档如何被识别为航天标签"的问题。

## 🔍 您的问题解答

### Q: 航天文档如何被分析出"航天"标签？

**A: 通过智能标签策略的主题分类算法**

1. **预定义主题词典**: 系统内置了航天领域的关键词库
   ```
   航天类关键词: ["航天", "太空", "火箭", "卫星", "宇宙", "航空", "飞行器", "探测器", "空间站", "载人航天", "月球", "火星", "行星", "轨道", "发射", "航天员", "宇航员", "NASA", "航天局"]
   ```

2. **内容匹配分析**: 当文档包含这些关键词时，系统会：
   - 统计匹配的关键词数量
   - 计算匹配度分数 = 匹配词数 / 总词库数
   - 如果分数超过阈值，自动生成"航天"标签

3. **多维度分析**: 系统同时分析：
   - 文档标题
   - 文档摘要  
   - 文档正文内容

### Q: 在哪里查询知识库的标签？

**A: 多种查询方式**

1. **前端界面查询**:
   - 在知识库列表中点击"查看"按钮
   - 系统会显示该知识库的所有标签及出现次数

2. **API接口查询**:
   ```
   GET /knowledge/build/knowledge-base/{id}/tags
   ```

3. **数据库直接查询**:
   ```sql
   -- 查看知识库的所有标签
   SELECT tags FROM knowledge_document WHERE knowledge_base_id = [知识库ID];
   ```

## 🏗️ 技术实现架构

### 1. 标签策略框架
```
TaggingStrategy (接口)
├── TaggingContext (上下文)
├── TaggingResult (结果)
└── DefaultTaggingStrategy (默认实现)
```

### 2. 标签生成算法

#### 主题分类算法
- **科技类**: 技术、AI、机器学习、大数据等
- **航天类**: 航天、太空、火箭、卫星等
- **医疗类**: 医疗、健康、医院、治疗等
- **教育类**: 教育、学校、学习、培训等
- **金融类**: 金融、银行、投资、理财等
- **商业类**: 商业、企业、市场、营销等
- **法律类**: 法律、法规、合同、诉讼等
- **环境类**: 环境、环保、生态、污染等

#### 关键词提取算法
- 中文分词处理
- TF（词频）计算
- 停用词过滤
- 最小词长限制

### 3. 标签存储机制
- **存储位置**: `knowledge_document.tags` 字段
- **存储格式**: 逗号分隔的字符串，如 "航天,科技,火箭"
- **更新时机**: 文档添加到知识库时自动生成

## 🚀 使用流程

### 1. 创建知识库时启用标签策略
1. 选择文档创建知识库
2. 点击"配置知识库策略"
3. 选择"智能标签生成策略"
4. 确认创建

### 2. 系统自动标签生成
1. 文档上传到向量数据库
2. 自动执行标签策略
3. 分析文档内容生成标签
4. 更新文档的tags字段

### 3. 查看标签结果
1. 在知识库列表点击"查看"
2. 查看标签统计信息
3. 了解标签分布和出现频次

## 📊 标签策略配置

### 默认配置
```json
{
  "maxTags": 5,
  "useAI": false,
  "keywordExtraction": true,
  "minKeywordLength": 2,
  "scoreThreshold": 0.1
}
```

### 配置说明
- **maxTags**: 最大标签数量
- **useAI**: 是否使用AI（当前为false，使用规则算法）
- **keywordExtraction**: 是否启用关键词提取
- **minKeywordLength**: 最小关键词长度
- **scoreThreshold**: 标签分数阈值

## 🧪 测试验证

### 1. 创建航天文档测试
1. 上传包含"火箭发射"、"卫星轨道"等内容的文档
2. 创建知识库并选择标签策略
3. 查看生成的标签是否包含"航天"

### 2. 数据库验证
```sql
-- 查看文档标签
SELECT name, tags FROM knowledge_document WHERE tags LIKE '%航天%';

-- 查看知识库标签统计
SELECT 
    kb.name as knowledge_base_name,
    COUNT(CASE WHEN kd.tags LIKE '%航天%' THEN 1 END) as aerospace_docs
FROM knowledge_base kb
LEFT JOIN knowledge_document kd ON kb.id = kd.knowledge_base_id
GROUP BY kb.id, kb.name;
```

### 3. API测试
```bash
# 查询知识库标签
curl -X GET "http://localhost:8080/knowledge/build/knowledge-base/1/tags"
```

## 🔧 扩展和优化

### 1. 增强算法
- 集成专业的中文分词工具（如jieba）
- 添加TF-IDF权重计算
- 引入词向量相似度计算

### 2. AI增强
- 集成大语言模型进行智能标签生成
- 使用预训练的文本分类模型
- 支持自定义领域词典

### 3. 用户交互
- 支持手动编辑标签
- 标签推荐和自动补全
- 标签层次结构管理

## 📝 总结

现在您的知识库系统具备了完整的智能标签功能：

✅ **自动识别**: 航天文档会被自动识别并打上"航天"标签
✅ **多维分析**: 基于标题、摘要、正文的综合分析
✅ **灵活查询**: 支持前端界面、API接口、数据库多种查询方式
✅ **可配置**: 支持标签数量、阈值等参数配置
✅ **可扩展**: 易于添加新的主题类别和算法

当您上传航天相关文档并使用标签策略创建知识库时，系统会自动分析内容并生成相应的标签，您可以通过点击"查看"按钮来查看生成的标签结果。
