-- 调试标签问题的SQL查询

-- 1. 查看知识库"航天"的基本信息
SELECT 
    id,
    name,
    description,
    document_count,
    status,
    create_time
FROM knowledge_base 
WHERE name LIKE '%航天%'
ORDER BY create_time DESC;

-- 2. 查看该知识库的文档信息
SELECT 
    kd.id,
    kd.name,
    kd.title,
    kd.tags,
    kd.knowledge_base_id,
    kd.type,
    kd.format,
    kd.status,
    kd.process_status,
    kd.create_time,
    LENGTH(kd.content) as content_length
FROM knowledge_document kd
JOIN knowledge_base kb ON kd.knowledge_base_id = kb.id
WHERE kb.name LIKE '%航天%'
ORDER BY kd.create_time DESC;

-- 3. 查看该知识库的策略配置
SELECT 
    kbsc.id,
    kbsc.knowledge_base_id,
    kb.name as knowledge_base_name,
    kbsc.strategy_template_id,
    kst.name as strategy_template_name,
    kbsc.strategy_type,
    kbsc.config_json,
    kbsc.is_enabled,
    kbsc.execution_order,
    kbsc.create_time
FROM knowledge_base_strategy_config kbsc
JOIN knowledge_base kb ON kbsc.knowledge_base_id = kb.id
LEFT JOIN knowledge_strategy_template kst ON kbsc.strategy_template_id = kst.id
WHERE kb.name LIKE '%航天%'
ORDER BY kbsc.execution_order;

-- 4. 查看所有文档的标签情况
SELECT 
    COUNT(*) as total_documents,
    COUNT(CASE WHEN tags IS NOT NULL AND tags != '' THEN 1 END) as documents_with_tags,
    ROUND(COUNT(CASE WHEN tags IS NOT NULL AND tags != '' THEN 1 END) * 100.0 / COUNT(*), 2) as tag_coverage_percent
FROM knowledge_document;

-- 5. 查看最近创建的文档（检查是否有标签）
SELECT 
    id,
    name,
    tags,
    knowledge_base_id,
    create_time,
    CASE 
        WHEN tags IS NULL OR tags = '' THEN '无标签'
        ELSE '有标签'
    END as tag_status
FROM knowledge_document
ORDER BY create_time DESC
LIMIT 10;
