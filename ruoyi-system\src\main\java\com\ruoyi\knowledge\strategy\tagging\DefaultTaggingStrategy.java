package com.ruoyi.knowledge.strategy.tagging;

import com.ruoyi.knowledge.strategy.AbstractKnowledgeStrategy;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 默认标签生成策略
 * 基于关键词提取和简单的主题分类
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Component
public class DefaultTaggingStrategy extends AbstractKnowledgeStrategy<TaggingContext, TaggingResult> 
        implements TaggingStrategy {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 预定义的主题词典
    private static final Map<String, List<String>> TOPIC_KEYWORDS = new HashMap<>();
    
    static {
        // 科技类
        TOPIC_KEYWORDS.put("科技", Arrays.asList("技术", "科技", "创新", "研发", "算法", "人工智能", "AI", "机器学习", "深度学习", "大数据", "云计算", "区块链", "物联网", "5G", "互联网", "软件", "硬件", "编程", "开发"));
        
        // 航天类
        TOPIC_KEYWORDS.put("航天", Arrays.asList("航天", "太空", "火箭", "卫星", "宇宙", "航空", "飞行器", "探测器", "空间站", "载人航天", "月球", "火星", "行星", "轨道", "发射", "航天员", "宇航员", "NASA", "航天局"));
        
        // 医疗健康
        TOPIC_KEYWORDS.put("医疗", Arrays.asList("医疗", "健康", "医院", "医生", "护士", "病人", "疾病", "治疗", "药物", "手术", "诊断", "康复", "预防", "疫苗", "临床", "医学", "健康管理", "医疗器械"));
        
        // 教育
        TOPIC_KEYWORDS.put("教育", Arrays.asList("教育", "学校", "学生", "老师", "教师", "课程", "学习", "培训", "知识", "技能", "考试", "学位", "大学", "中学", "小学", "幼儿园", "在线教育", "远程教育"));
        
        // 金融
        TOPIC_KEYWORDS.put("金融", Arrays.asList("金融", "银行", "投资", "理财", "保险", "股票", "基金", "债券", "贷款", "信贷", "支付", "货币", "汇率", "风险", "收益", "资产", "财务", "会计"));
        
        // 商业
        TOPIC_KEYWORDS.put("商业", Arrays.asList("商业", "企业", "公司", "市场", "营销", "销售", "客户", "产品", "服务", "品牌", "竞争", "战略", "管理", "运营", "供应链", "电商", "零售", "批发"));
        
        // 法律
        TOPIC_KEYWORDS.put("法律", Arrays.asList("法律", "法规", "条例", "合同", "协议", "诉讼", "仲裁", "律师", "法官", "法院", "权利", "义务", "责任", "违法", "犯罪", "刑事", "民事", "行政"));
        
        // 环境
        TOPIC_KEYWORDS.put("环境", Arrays.asList("环境", "环保", "生态", "污染", "清洁", "绿色", "可持续", "节能", "减排", "碳中和", "气候", "全球变暖", "新能源", "太阳能", "风能", "水资源", "森林", "海洋"));
    }
    
    @Override
    public String getStrategyName() {
        return "默认标签生成策略";
    }
    
    @Override
    public String getStrategyDescription() {
        return "基于关键词提取和主题分类的智能标签生成策略";
    }
    
    @Override
    public String getDefaultConfig() {
        return "{\"maxTags\": 5, \"useAI\": false, \"keywordExtraction\": true, \"minKeywordLength\": 2, \"scoreThreshold\": 0.1}";
    }
    
    @Override
    public boolean validateConfig(String config) {
        try {
            JsonNode node = objectMapper.readTree(config);
            return node.has("maxTags") && node.get("maxTags").isInt() && node.get("maxTags").asInt() > 0;
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    protected TaggingResult doExecute(TaggingContext input, String config) throws Exception {
        long startTime = System.currentTimeMillis();
        TaggingResult result = new TaggingResult();
        
        try {
            // 解析配置
            JsonNode configNode = objectMapper.readTree(config != null ? config : getDefaultConfig());
            int maxTags = configNode.path("maxTags").asInt(5);
            boolean keywordExtraction = configNode.path("keywordExtraction").asBoolean(true);
            int minKeywordLength = configNode.path("minKeywordLength").asInt(2);
            double scoreThreshold = configNode.path("scoreThreshold").asDouble(0.1);
            
            // 获取文本内容
            String text = buildFullText(input);
            if (text == null || text.trim().isEmpty()) {
                result.setSuccess(false);
                result.setMessage("文档内容为空");
                return result;
            }
            
            // 生成标签
            List<TaggingResult.Tag> tags = new ArrayList<>();
            
            // 1. 主题分类标签
            tags.addAll(generateTopicTags(text));
            
            // 2. 关键词提取标签
            if (keywordExtraction) {
                tags.addAll(generateKeywordTags(text, minKeywordLength));
            }
            
            // 3. 过滤和排序标签
            tags = tags.stream()
                    .filter(tag -> tag.getScore() >= scoreThreshold)
                    .sorted((a, b) -> Double.compare(b.getScore(), a.getScore()))
                    .limit(maxTags)
                    .collect(Collectors.toList());
            
            result.setTags(tags);
            result.setSuccess(true);
            result.setMessage("标签生成成功，共生成 " + tags.size() + " 个标签");
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("标签生成失败: " + e.getMessage());
            logger.error("标签生成失败", e);
        }
        
        result.setExecutionTime(System.currentTimeMillis() - startTime);
        return result;
    }
    
    /**
     * 构建完整文本
     */
    private String buildFullText(TaggingContext input) {
        StringBuilder text = new StringBuilder();
        
        if (input.getTitle() != null && !input.getTitle().trim().isEmpty()) {
            text.append(input.getTitle()).append(" ");
        }
        
        if (input.getSummary() != null && !input.getSummary().trim().isEmpty()) {
            text.append(input.getSummary()).append(" ");
        }
        
        if (input.getContent() != null && !input.getContent().trim().isEmpty()) {
            text.append(input.getContent());
        }
        
        return text.toString().trim();
    }
    
    /**
     * 生成主题分类标签
     */
    private List<TaggingResult.Tag> generateTopicTags(String text) {
        List<TaggingResult.Tag> topicTags = new ArrayList<>();
        String lowerText = text.toLowerCase();
        
        for (Map.Entry<String, List<String>> entry : TOPIC_KEYWORDS.entrySet()) {
            String topic = entry.getKey();
            List<String> keywords = entry.getValue();
            
            int matchCount = 0;
            for (String keyword : keywords) {
                if (lowerText.contains(keyword.toLowerCase())) {
                    matchCount++;
                }
            }
            
            if (matchCount > 0) {
                double score = (double) matchCount / keywords.size();
                topicTags.add(new TaggingResult.Tag(topic, score, "主题"));
            }
        }
        
        return topicTags;
    }
    
    /**
     * 生成关键词标签
     */
    private List<TaggingResult.Tag> generateKeywordTags(String text, int minLength) {
        List<TaggingResult.Tag> keywordTags = new ArrayList<>();
        
        // 简单的中文分词和关键词提取
        Map<String, Integer> wordFreq = extractKeywords(text, minLength);
        
        // 计算TF分数
        int totalWords = wordFreq.values().stream().mapToInt(Integer::intValue).sum();
        
        for (Map.Entry<String, Integer> entry : wordFreq.entrySet()) {
            String word = entry.getKey();
            int freq = entry.getValue();
            double score = (double) freq / totalWords;
            
            if (freq >= 2) { // 至少出现2次
                keywordTags.add(new TaggingResult.Tag(word, score, "关键词"));
            }
        }
        
        return keywordTags;
    }
    
    /**
     * 简单的关键词提取
     */
    private Map<String, Integer> extractKeywords(String text, int minLength) {
        Map<String, Integer> wordFreq = new HashMap<>();
        
        // 移除标点符号和特殊字符
        String cleanText = text.replaceAll("[\\p{Punct}\\s]+", " ");
        
        // 简单的中文词汇提取（这里使用简单的方法，实际项目中建议使用专业的中文分词工具）
        Pattern chinesePattern = Pattern.compile("[\\u4e00-\\u9fa5]+");
        java.util.regex.Matcher matcher = chinesePattern.matcher(cleanText);
        
        while (matcher.find()) {
            String word = matcher.group();
            if (word.length() >= minLength && !isStopWord(word)) {
                wordFreq.put(word, wordFreq.getOrDefault(word, 0) + 1);
            }
        }
        
        return wordFreq;
    }
    
    /**
     * 判断是否为停用词
     */
    private boolean isStopWord(String word) {
        Set<String> stopWords = Set.of("的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", "一", "一个", "上", "也", "很", "到", "说", "要", "去", "你", "会", "着", "没有", "看", "好", "自己", "这");
        return stopWords.contains(word);
    }
}
