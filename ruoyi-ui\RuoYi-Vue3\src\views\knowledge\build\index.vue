<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 左侧目录树 -->
      <el-col :span="6" :xs="24">
        <div class="knowledge-sidebar">
          <div class="sidebar-header">
            <h3>知识库管理</h3>
          </div>
          <div class="sidebar-content">
            <!-- 知识库列表 -->
            <div class="knowledge-base-section">
              <div class="section-header">
                <h4>已构建知识库</h4>
                <el-button link size="small" @click="refreshKnowledgeBaseList" title="刷新知识库列表">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
              <div class="knowledge-base-list">
                <div
                  v-for="kb in knowledgeBaseList"
                  :key="kb.id"
                  class="knowledge-base-item"
                  :class="{ active: selectedKnowledgeBase?.id === kb.id }"
                  @click="handleKnowledgeBaseClick(kb)"
                >
                  <div class="kb-info">
                    <el-icon class="kb-icon"><DataBoard /></el-icon>
                    <div class="kb-details">
                      <div class="kb-name">{{ kb.name }}</div>
                      <div class="kb-meta">
                        <span class="kb-doc-count">{{ kb.documentCount }}个文档</span>
                        <el-tag
                          :type="kb.status === '0' ? 'success' : 'warning'"
                          size="small"
                        >
                          {{ kb.status === '0' ? '正常' : '构建中' }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                  <div class="kb-actions">
                    <el-button
                      link
                      size="small"
                      @click.stop="handleViewKnowledgeBase(kb)"
                    >
                      查看
                    </el-button>
                    <el-button
                      link
                      size="small"
                      type="danger"
                      @click.stop="handleDeleteKnowledgeBase(kb)"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
                <div v-if="knowledgeBaseList.length === 0" class="empty-state">
                  <el-empty description="暂无知识库" :image-size="60" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>

      <!-- 右侧内容区域 -->
      <el-col :span="18" :xs="24">
        <div class="knowledge-content">
          <!-- 搜索栏 -->
          <div class="search-bar">
            <el-input
              v-model="searchQuery"
              placeholder="搜索内容"
              prefix-icon="Search"
              clearable
              style="width: 300px; margin-right: 10px;"
              @keyup.enter="handleSearch"
            />
            <el-button type="primary" icon="Search" @click="handleSearch">
              搜索
            </el-button>
          </div>

          <!-- 工具栏 -->
          <div class="toolbar">
            <el-button type="primary" icon="Plus" @click="handleCreateDocument">
              创建文档
            </el-button>
            <el-button type="success" icon="Upload" @click="handleUploadDocument">
              上传文档
            </el-button>
            <el-button type="info" icon="Download" @click="handleImportDocument">
              批量导入
            </el-button>
            <el-button
              type="danger"
              icon="DataBoard"
              @click="handleCreateKnowledgeBase"
              :disabled="selectedDocuments.length === 0"
            >
              创建知识库 ({{ selectedDocuments.length }})
            </el-button>
            <el-button type="warning" icon="Refresh" @click="handleRefresh">
              刷新
            </el-button>
          </div>

          <!-- 文档列表 -->
          <div class="document-list">
            <el-table
              v-loading="loading"
              :data="documentList"
              style="width: 100%"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="name" label="文档名称" min-width="200">
                <template #default="scope">
                  <el-link type="primary" @click="handleViewDocument(scope.row)">
                    <el-icon v-if="scope.row.type === 'image'"><Picture /></el-icon>
                    <el-icon v-else><Document /></el-icon>
                    {{ scope.row.name }}
                  </el-link>
                </template>
              </el-table-column>
              <el-table-column prop="type" label="类型" width="100">
                <template #default="scope">
                  <el-tag :type="getDocumentTypeTag(scope.row.type)">
                    {{ scope.row.type }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="size" label="大小" width="100" />
              <el-table-column prop="updateTime" label="更新时间" width="180" />
              <el-table-column prop="processStatus" label="处理状态" width="100">
                <template #default="scope">
                  <el-tag :type="getProcessStatusType(scope.row.processStatus)">
                    {{ getProcessStatusText(scope.row.processStatus) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" fixed="right">
                <template #default="scope">
                  <el-button v-if="scope.row.type === 'image'" link size="small" @click="handlePreviewImage(scope.row)">
                    预览
                  </el-button>
                  <el-button link size="small" @click="handleProcessDocument(scope.row)">
                    处理
                  </el-button>
                  <el-button link size="small" @click="handleDeleteDocument(scope.row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getDocumentList"
            />
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 创建文档对话框 -->
    <el-dialog title="创建文档" v-model="createDocumentVisible" width="600px" append-to-body>
      <el-form ref="documentFormRef" :model="documentForm" :rules="documentRules" label-width="100px">
        <el-form-item label="文档名称" prop="name">
          <el-input v-model="documentForm.name" placeholder="请输入文档名称" />
        </el-form-item>
        <el-form-item label="文档类型" prop="type">
          <el-select v-model="documentForm.type" placeholder="请选择文档类型">
            <el-option label="Markdown" value="md" />
            <el-option label="Word文档" value="docx" />
            <el-option label="PDF文档" value="pdf" />
            <el-option label="文本文档" value="txt" />
          </el-select>
        </el-form-item>
        <el-form-item label="文档描述" prop="description">
          <el-input
            v-model="documentForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入文档描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="createDocumentVisible = false">取消</el-button>
          <el-button type="primary" @click="submitCreateDocument">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 上传文档对话框 -->
    <el-dialog title="上传文档" v-model="uploadDocumentVisible" width="600px" append-to-body>
      <el-upload
        ref="uploadRef"
        :action="uploadUrl"
        :headers="uploadHeaders"
        :file-list="fileList"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :on-progress="handleUploadProgress"
        :on-change="handleFileChange"
        :before-upload="beforeUpload"
        :auto-upload="false"
        multiple
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持 .pdf, .docx, .doc, .xlsx, .xls, .txt, .md, .jpg, .jpeg, .png, .gif, .bmp 格式文件，单个文件大小不超过 10MB
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelUpload">取消</el-button>
          <el-button type="primary" @click="submitUpload" :disabled="fileList.length === 0">
            开始上传 ({{ fileList.length }})
          </el-button>
          <el-button @click="testUpload" v-if="fileList.length > 0">
            测试上传
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 创建知识库对话框 -->
    <el-dialog title="创建知识库" v-model="createKnowledgeBaseVisible" width="600px" append-to-body>
      <el-form ref="knowledgeBaseFormRef" :model="knowledgeBaseForm" :rules="knowledgeBaseRules" label-width="100px">
        <el-form-item label="知识库名称" prop="name">
          <el-input v-model="knowledgeBaseForm.name" placeholder="请输入知识库名称" />
        </el-form-item>
        <el-form-item label="知识库描述" prop="description">
          <el-input
            v-model="knowledgeBaseForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入知识库描述"
          />
        </el-form-item>
        <el-form-item label="选中文档">
          <div class="selected-documents">
            <el-tag
              v-for="doc in selectedDocuments"
              :key="doc.id"
              closable
              @close="removeSelectedDocument(doc)"
              style="margin-right: 8px; margin-bottom: 8px;"
            >
              {{ doc.name }}
            </el-tag>
            <div v-if="selectedDocuments.length === 0" class="no-documents">
              请先在文档列表中选择要添加到知识库的文档
            </div>
          </div>
        </el-form-item>
        <el-form-item label="策略配置">
          <div class="strategy-config-section">
            <el-button
              type="primary"
              icon="Setting"
              @click="openStrategyConfigDialog"
              class="strategy-config-button"
            >
              配置知识库策略 ({{ selectedStrategies.length }})
            </el-button>
            <div v-if="selectedStrategies.length > 0" class="selected-strategies">
              <el-tag
                v-for="strategy in selectedStrategies"
                :key="strategy.id"
                type="success"
                style="margin-right: 8px; margin-top: 8px;"
              >
                {{ strategy.name }}
              </el-tag>
            </div>
            <div v-else class="no-strategies">
              未选择策略，将使用系统默认配置
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="createKnowledgeBaseVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="submitCreateKnowledgeBase"
            :loading="knowledgeBaseCreating"
            :disabled="selectedDocuments.length === 0"
          >
            {{ knowledgeBaseCreating ? '创建中...' : '创建知识库' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog title="图片预览" v-model="imagePreviewVisible" width="800px" append-to-body>
      <div class="image-preview-container" style="text-align: center;">
        <img
          :src="previewImageUrl"
          :alt="previewImageName"
          style="max-width: 100%; max-height: 500px; object-fit: contain;"
          @error="handleImageError"
        />
        <div class="image-info" style="margin-top: 20px; color: #666;">
          <p><strong>文件名：</strong>{{ previewImageName }}</p>
          <p><strong>文件大小：</strong>{{ previewImageSize }}</p>
          <p><strong>文件格式：</strong>{{ previewImageFormat }}</p>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="imagePreviewVisible = false">关闭</el-button>
          <el-button type="primary" @click="downloadImage">下载</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 策略配置对话框 -->
    <el-dialog title="知识库策略配置" v-model="strategyConfigVisible" width="900px" append-to-body>
      <div class="strategy-config-container">
        <el-tabs v-model="activeStrategyTab" type="card">
          <el-tab-pane
            v-for="strategyType in strategyTypes"
            :key="strategyType.code"
            :label="strategyType.name"
            :name="strategyType.code"
          >
            <div class="strategy-type-content">
              <div class="strategy-type-description">
                <el-alert :title="strategyType.description" type="info" show-icon :closable="false" />
              </div>
              <div class="strategy-enable-section">
                <el-checkbox
                  v-model="strategyEnabled[strategyType.code]"
                  @change="handleStrategyEnabledChange(strategyType.code)"
                  style="margin-bottom: 16px;"
                >
                  启用{{ strategyType.name }}
                </el-checkbox>
              </div>
              <div v-if="strategyEnabled[strategyType.code]" class="strategy-templates">
                <div class="default-strategy-info">
                  <i class="el-icon-info" style="color: #409eff; margin-right: 8px;"></i>
                  <span style="color: #606266;">将使用默认的{{ strategyType.name }}</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="strategyConfigVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmStrategyConfig">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="KnowledgeBuild">
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Picture } from '@element-plus/icons-vue'
import { getToken } from '@/utils/auth'
import Pagination from '@/components/Pagination'
import {
  listDocument,
  searchDocuments,
  addDocument,
  delDocument,
  uploadDocument,
  batchUploadDocuments,
  processDocument,
  createKnowledgeBase,
  listKnowledgeBase,
  getKnowledgeBase,
  delKnowledgeBase
} from '@/api/knowledge/build'
import {
  getStrategyTypes,
  getEnabledTemplates,
  getTemplatesByType
} from '@/api/knowledge/strategy'

const { proxy } = getCurrentInstance()

// 响应式数据
const loading = ref(false)
const searchQuery = ref('')
const total = ref(0)
const createDocumentVisible = ref(false)
const uploadDocumentVisible = ref(false)
const createKnowledgeBaseVisible = ref(false)
const knowledgeBaseCreating = ref(false)
const selectedDocuments = ref([])
const fileList = ref([])

// 图片预览相关数据
const imagePreviewVisible = ref(false)
const previewImageUrl = ref('')
const previewImageName = ref('')
const previewImageSize = ref('')
const previewImageFormat = ref('')

// 知识库相关数据
const knowledgeBaseList = ref([])
const selectedKnowledgeBase = ref(null)
const knowledgeBaseLoading = ref(false)

// 策略相关数据
const strategyConfigVisible = ref(false)
const strategyTypes = ref([])
const strategyTemplatesByType = ref({})
const selectedStrategyTemplates = ref({})
const strategyEnabled = ref({}) // 策略启用状态
const selectedStrategies = ref([])
const activeStrategyTab = ref('')

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  keyword: ''
})

// 文档表单
const documentForm = reactive({
  name: '',
  type: '',
  description: ''
})

// 表单验证规则
const documentRules = {
  name: [{ required: true, message: '文档名称不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '请选择文档类型', trigger: 'change' }]
}

// 知识库表单
const knowledgeBaseForm = reactive({
  name: '',
  description: ''
})

// 知识库表单验证规则
const knowledgeBaseRules = {
  name: [{ required: true, message: '知识库名称不能为空', trigger: 'blur' }]
}

// 上传配置
const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + '/knowledge/build/document/upload')
const uploadHeaders = ref({ Authorization: 'Bearer ' + getToken() })

// 模拟数据已删除，使用真实API数据

const documentList = ref([
  {
    id: 1,
    name: '产品需求文档.docx',
    type: 'docx',
    size: '2.5MB',
    updateTime: '2024-01-15 10:30:00',
    status: '已处理'
  },
  {
    id: 2,
    name: 'API接口文档.md',
    type: 'md',
    size: '1.2MB',
    updateTime: '2024-01-14 16:45:00',
    status: '处理中'
  }
])

// 方法
const getDocumentList = () => {
  loading.value = true
  listDocument(queryParams).then(response => {
    documentList.value = response.rows
    total.value = response.total
    loading.value = false
  }).catch(() => {
    loading.value = false
  })
}

const handleSearch = () => {
  if (searchQuery.value.trim()) {
    loading.value = true
    searchDocuments(searchQuery.value).then(response => {
      documentList.value = response.data
      total.value = response.data.length
      loading.value = false
    }).catch(() => {
      loading.value = false
    })
  } else {
    queryParams.keyword = ''
    queryParams.pageNum = 1
    getDocumentList()
  }
}

const handleRefresh = () => {
  searchQuery.value = ''
  queryParams.keyword = ''
  queryParams.pageNum = 1
  getDocumentList()
}

// handleFolderClick 方法已删除

const handleCreateDocument = () => {
  createDocumentVisible.value = true
}

const handleUploadDocument = () => {
  uploadDocumentVisible.value = true
}

const handleImportDocument = () => {
  ElMessage.info('批量导入功能开发中...')
}

const handleSelectionChange = (selection) => {
  selectedDocuments.value = selection
}

const getDocumentTypeTag = (type) => {
  const typeMap = {
    'markdown': 'info',
    'md': 'info',
    'word': 'success',
    'docx': 'success',
    'doc': 'success',
    'pdf': 'warning',
    'text': '',
    'txt': '',
    'excel': 'success',
    'xls': 'success',
    'xlsx': 'success',
    'document': 'info'
  }
  return typeMap[type] || 'info'
}

// 获取处理状态文本
const getProcessStatusText = (status) => {
  const statusMap = {
    '0': '未处理',
    '1': '已处理',
    '2': '处理失败'
  }
  return statusMap[status] || '未知'
}

// 获取处理状态标签类型
const getProcessStatusType = (status) => {
  const typeMap = {
    '0': 'warning',
    '1': 'success',
    '2': 'danger'
  }
  return typeMap[status] || 'info'
}



const handleProcessDocument = (row) => {
  processDocument(row.id).then(() => {
    ElMessage.success(`文档 "${row.name}" 处理成功`)
    getDocumentList()
  }).catch(() => {
    ElMessage.error(`文档 "${row.name}" 处理失败`)
  })
}

const handleDeleteDocument = (row) => {
  ElMessageBox.confirm(`确认删除文档 "${row.name}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    delDocument(row.id).then(() => {
      ElMessage.success('删除成功')
      getDocumentList()
    })
  })
}

const submitCreateDocument = () => {
  proxy.$refs.documentFormRef.validate((valid) => {
    if (valid) {
      addDocument(documentForm).then(() => {
        ElMessage.success('文档创建成功')
        createDocumentVisible.value = false
        resetDocumentForm()
        getDocumentList()
      })
    }
  })
}

const resetDocumentForm = () => {
  documentForm.name = ''
  documentForm.type = ''
  documentForm.description = ''
}

const beforeUpload = (file) => {
  // 支持的文件类型和对应的MIME类型
  const allowedTypes = [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
    'application/msword', // .doc
    'text/plain', // .txt
    'text/markdown', // .md
    'application/vnd.ms-excel', // .xls
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    // 图片类型
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/bmp'
  ]

  // 支持的文件扩展名
  const allowedExtensions = ['pdf', 'docx', 'doc', 'txt', 'md', 'xls', 'xlsx', 'jpg', 'jpeg', 'png', 'gif', 'bmp']

  // 获取文件扩展名
  const fileName = file.name.toLowerCase()
  const fileExtension = fileName.substring(fileName.lastIndexOf('.') + 1)

  // 检查文件类型（通过MIME类型或扩展名）
  const isAllowedType = allowedTypes.includes(file.type) || allowedExtensions.includes(fileExtension)
  const isLt10M = file.size / 1024 / 1024 < 10

  console.log('文件信息:', {
    name: file.name,
    type: file.type,
    extension: fileExtension,
    size: (file.size / 1024 / 1024).toFixed(2) + 'MB',
    isAllowedType: isAllowedType
  })

  if (!isAllowedType) {
    ElMessage.error('只能上传 PDF、Word、Excel、文本、Markdown 或图片文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
    return false
  }

  ElMessage.info('正在上传文件，请稍候...')
  return true
}

const handleUploadSuccess = (response, file) => {
  console.log('上传成功响应:', response)
  if (response.code === 200) {
    ElMessage.success(`${file.name} 上传成功`)
    uploadDocumentVisible.value = false
    fileList.value = []
    getDocumentList()
  } else {
    ElMessage.error(`${file.name} 上传失败: ${response.msg || '未知错误'}`)
  }
}

const handleUploadError = (error, file) => {
  console.error('上传失败:', error)
  let errorMsg = '上传失败'

  try {
    if (error.response && error.response.data) {
      errorMsg = error.response.data.msg || error.response.data.message || errorMsg
    } else if (error.message) {
      errorMsg = error.message
    }
  } catch (e) {
    console.error('解析错误信息失败:', e)
  }

  ElMessage.error(`${file.name} ${errorMsg}`)
}

const handleUploadProgress = (event, file, fileList) => {
  console.log('上传进度:', Math.round(event.percent), '%')
}

const handleFileChange = (file, fileListParam) => {
  console.log('文件列表变化:', fileListParam)
  console.log('当前文件:', file)
  fileList.value = fileListParam

  // 额外的调试信息
  console.log('文件列表长度:', fileListParam.length)
  if (fileListParam.length > 0) {
    console.log('第一个文件信息:', {
      name: fileListParam[0].name,
      size: fileListParam[0].size,
      type: fileListParam[0].raw?.type,
      status: fileListParam[0].status
    })
  }
}

const submitUpload = () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择要上传的文件')
    return
  }

  console.log('开始上传文件:', fileList.value)
  console.log('上传URL:', uploadUrl.value)
  console.log('上传Headers:', uploadHeaders.value)

  // 检查上传组件引用
  if (!proxy.$refs.uploadRef) {
    ElMessage.error('上传组件未正确初始化')
    return
  }

  try {
    proxy.$refs.uploadRef.submit()
  } catch (error) {
    console.error('上传提交失败:', error)
    ElMessage.error('上传提交失败: ' + error.message)
  }
}

// 知识库管理方法
const getKnowledgeBaseList = () => {
  knowledgeBaseLoading.value = true
  listKnowledgeBase({}).then(response => {
    knowledgeBaseList.value = response.rows || []
    knowledgeBaseLoading.value = false
  }).catch(() => {
    knowledgeBaseLoading.value = false
  })
}

const refreshKnowledgeBaseList = () => {
  getKnowledgeBaseList()
}

const handleKnowledgeBaseClick = (knowledgeBase) => {
  selectedKnowledgeBase.value = knowledgeBase
  // 可以在这里加载该知识库的相关文档
  queryParams.knowledgeBaseId = knowledgeBase.id
  getDocumentList()
}

const handleViewKnowledgeBase = (knowledgeBase) => {
  ElMessage.info(`查看知识库: ${knowledgeBase.name}`)
  // 这里可以跳转到知识库详情页面或打开详情对话框
}

const handleDeleteKnowledgeBase = (knowledgeBase) => {
  ElMessageBox.confirm(
    `确定要删除知识库"${knowledgeBase.name}"吗？此操作不可恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    delKnowledgeBase([knowledgeBase.id]).then(response => {
      if (response.code === 200) {
        ElMessage.success('知识库删除成功')
        getKnowledgeBaseList()
        // 如果删除的是当前选中的知识库，清空选中状态
        if (selectedKnowledgeBase.value?.id === knowledgeBase.id) {
          selectedKnowledgeBase.value = null
        }
      } else {
        ElMessage.error(response.msg || '删除失败')
      }
    }).catch(error => {
      ElMessage.error('删除失败: ' + error.message)
    })
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 创建知识库相关方法
const handleCreateKnowledgeBase = () => {
  if (selectedDocuments.value.length === 0) {
    ElMessage.warning('请先选择要添加到知识库的文档')
    return
  }

  // 重置表单
  knowledgeBaseForm.name = ''
  knowledgeBaseForm.description = ''

  createKnowledgeBaseVisible.value = true
}

const removeSelectedDocument = (doc) => {
  const index = selectedDocuments.value.findIndex(item => item.id === doc.id)
  if (index > -1) {
    selectedDocuments.value.splice(index, 1)
  }
}

const submitCreateKnowledgeBase = async () => {
  try {
    // 表单验证
    await proxy.$refs.knowledgeBaseFormRef.validate()

    if (selectedDocuments.value.length === 0) {
      ElMessage.warning('请选择要添加到知识库的文档')
      return
    }

    knowledgeBaseCreating.value = true

    // 构建策略配置
    console.log('🚀 开始构建策略配置，选中的策略数量:', selectedStrategies.value.length)
    console.log('🚀 选中的策略详情:', selectedStrategies.value)

    const strategyConfigs = selectedStrategies.value.map((strategy, index) => {
      const config = {
        strategyTemplateId: strategy.templateId,
        strategyType: strategy.strategyType,
        configJson: null, // 使用模板默认配置
        enabled: true,
        executionOrder: index + 1
      }
      console.log(`✅ 策略配置 ${index + 1}:`, config)
      return config
    })

    const requestData = {
      name: knowledgeBaseForm.name,
      description: knowledgeBaseForm.description,
      documentIds: selectedDocuments.value.map(doc => doc.id),
      strategyConfigs: strategyConfigs
    }

    console.log('🚀 创建知识库请求数据:', requestData)
    console.log('🚀 策略配置数量:', strategyConfigs.length)

    if (strategyConfigs.length === 0) {
      console.log('⚠️ 警告：没有选择任何策略，将使用系统默认策略')
    } else {
      console.log('✅ 将执行以下策略:')
      strategyConfigs.forEach((config, index) => {
        console.log(`  ${index + 1}. ${config.strategyType} (模板ID: ${config.strategyTemplateId})`)
      })
    }

    // 调用API创建知识库
    console.log('🚀 开始调用创建知识库API')
    const response = await createKnowledgeBase(requestData)
    console.log('✅ 创建知识库API调用完成，响应:', response)

    if (response.code === 200) {
      console.log('🎉 知识库创建成功！')
      console.log('🎉 知识库ID:', response.data?.id)

      // 显示策略执行结果
      if (strategyConfigs.length > 0) {
        console.log('🎉 策略执行成功！执行的策略:')
        strategyConfigs.forEach((config, index) => {
          console.log(`  ✅ ${index + 1}. ${config.strategyType} 策略执行成功`)
        })
        ElMessage.success(`知识库创建成功！已执行 ${strategyConfigs.length} 个策略`)
      } else {
        ElMessage.success('知识库创建成功！使用默认配置')
      }

      createKnowledgeBaseVisible.value = false
      selectedDocuments.value = []
      selectedStrategies.value = [] // 清空选中的策略

      // 刷新文档列表和知识库列表
      getDocumentList()
      getKnowledgeBaseList()
    } else {
      console.error('❌ 知识库创建失败:', response.msg)
      ElMessage.error(response.msg || '知识库创建失败')
    }

  } catch (error) {
    console.error('创建知识库失败:', error)
    ElMessage.error('创建知识库失败: ' + (error.message || '未知错误'))
  } finally {
    knowledgeBaseCreating.value = false
  }
}

const cancelUpload = () => {
  uploadDocumentVisible.value = false
  fileList.value = []
  proxy.$refs.uploadRef.clearFiles()
}

// 测试上传函数
const testUpload = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('没有文件可以测试')
    return
  }

  const file = fileList.value[0]
  console.log('测试上传文件:', file)

  // 创建 FormData
  const formData = new FormData()
  formData.append('file', file.raw)

  try {
    const response = await fetch(uploadUrl.value, {
      method: 'POST',
      headers: {
        'Authorization': uploadHeaders.value.Authorization
      },
      body: formData
    })

    const result = await response.json()
    console.log('测试上传结果:', result)

    if (result.code === 200) {
      ElMessage.success('测试上传成功')
    } else {
      ElMessage.error('测试上传失败: ' + result.msg)
    }
  } catch (error) {
    console.error('测试上传错误:', error)
    ElMessage.error('测试上传出错: ' + error.message)
  }
}

// 图片预览相关方法
const handlePreviewImage = (document) => {
  // 构建图片URL
  const baseUrl = import.meta.env.VITE_APP_BASE_API
  previewImageUrl.value = baseUrl + document.fileUrl
  previewImageName.value = document.name
  previewImageSize.value = formatFileSize(document.size)
  previewImageFormat.value = document.format?.toUpperCase() || 'Unknown'
  imagePreviewVisible.value = true
}

const handleImageError = () => {
  ElMessage.error('图片加载失败')
}

const downloadImage = () => {
  // 创建一个临时的a标签来下载图片
  const link = document.createElement('a')
  link.href = previewImageUrl.value
  link.download = previewImageName.value
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 策略相关方法
const initializeDefaultStrategyData = () => {
  console.log('初始化默认策略数据')

  // 设置默认策略类型
  strategyTypes.value = [
    { code: 'INITIALIZATION', name: '初始化策略', description: '控制知识库的创建和初始化过程' },
    { code: 'SEGMENTATION', name: '段落切分策略', description: '控制文档如何分割成段落' },
    { code: 'TAGGING', name: '标签策略', description: '为知识段落自动生成标签' },
    { code: 'SUMMARIZATION', name: '归纳总结策略', description: '对知识内容进行归纳总结' },
    { code: 'ASSOCIATION', name: '关联策略', description: '建立知识之间的关联关系' }
  ]

  // 设置默认策略模板
  strategyTemplatesByType.value = {
    'INITIALIZATION': [
      { id: 1, name: '默认初始化策略', description: '知识库的默认初始化策略，包含基础的创建和配置流程', isDefault: '1' },
      { id: 2, name: '快速初始化策略', description: '快速初始化策略，适合小型知识库', isDefault: '0' }
    ],
    'SEGMENTATION': [
      { id: 3, name: '固定长度分割策略', description: '按照固定字符数量分割文档', isDefault: '1' },
      { id: 4, name: '语义分割策略', description: '基于语义边界分割文档', isDefault: '0' },
      { id: 5, name: '句子分割策略', description: '按照句子边界分割文档', isDefault: '0' }
    ],
    'TAGGING': [
      { id: 6, name: '关键词提取标签策略', description: '基于词频和TF-IDF提取关键词', isDefault: '1' },
      { id: 7, name: '主题分类标签策略', description: '基于主题词典进行文档分类', isDefault: '0' }
    ],
    'SUMMARIZATION': [
      { id: 8, name: '抽取式摘要策略', description: '从原文中抽取关键句子形成摘要', isDefault: '1' }
    ],
    'ASSOCIATION': [
      { id: 9, name: '相似度关联策略', description: '基于向量相似度发现相关文档', isDefault: '1' }
    ]
  }

  // 初始化策略启用状态（默认都不启用）
  strategyEnabled.value = {}
  selectedStrategyTemplates.value = {}

  for (const strategyType of strategyTypes.value) {
    strategyEnabled.value[strategyType.code] = false // 默认不启用
    // 为每种策略类型设置默认模板（但不启用）
    const templates = strategyTemplatesByType.value[strategyType.code]
    if (templates && templates.length > 0) {
      const defaultTemplate = templates.find(t => t.isDefault === '1') || templates[0]
      selectedStrategyTemplates.value[strategyType.code] = defaultTemplate.id
    }
  }

  // 设置默认活动标签页
  if (strategyTypes.value.length > 0) {
    activeStrategyTab.value = strategyTypes.value[0].code
  }

  console.log('默认策略数据初始化完成')
}

const loadStrategyTypes = async () => {
  try {
    console.log('🚀 真实API调用: getStrategyTypes()')
    const response = await getStrategyTypes()
    console.log('📡 API响应:', response)

    if (response.code === 200 && response.data && response.data.length > 0) {
      strategyTypes.value = response.data
      console.log('✅ 真实API成功: 获得', strategyTypes.value.length, '个策略类型')
      console.log('📊 策略类型数据:', strategyTypes.value)

      if (strategyTypes.value.length > 0) {
        activeStrategyTab.value = strategyTypes.value[0].code
        console.log('✅ 设置活动标签页:', activeStrategyTab.value)
      }
    } else {
      console.error('❌ API返回失败或空数据:', response)
      throw new Error(`API返回错误: code=${response.code}, msg=${response.msg}`)
    }
  } catch (error) {
    console.error('❌ 策略类型API调用失败:', error)
    throw error // 不再使用假数据，直接抛出错误
  }
}

const loadStrategyTemplates = async () => {
  try {
    console.log('🚀 真实API调用: 加载策略模板，策略类型数量:', strategyTypes.value.length)

    let successCount = 0
    let totalTemplateCount = 0

    for (const strategyType of strategyTypes.value) {
      console.log(`🚀 真实API调用: getTemplatesByType(${strategyType.code})`)
      const response = await getTemplatesByType(strategyType.code)
      console.log(`📡 API响应 ${strategyType.code}:`, response)

      if (response.code === 200 && response.data && response.data.length > 0) {
        strategyTemplatesByType.value[strategyType.code] = response.data
        totalTemplateCount += response.data.length
        successCount++

        console.log(`✅ 真实API成功: ${strategyType.code} 获得 ${response.data.length} 个模板`)
        console.log(`📊 ${strategyType.code} 模板数据:`, response.data)

        // 设置默认选中的模板
        const defaultTemplate = response.data.find(t => t.isDefault === '1')
        if (defaultTemplate) {
          selectedStrategyTemplates.value[strategyType.code] = defaultTemplate.id
        } else if (response.data.length > 0) {
          selectedStrategyTemplates.value[strategyType.code] = response.data[0].id
        }
      } else {
        console.error(`❌ API失败: ${strategyType.code} 模板加载失败:`, response)
        throw new Error(`${strategyType.code} 策略模板API调用失败`)
      }
    }

    console.log(`✅ 所有策略模板API调用成功! 成功: ${successCount}/${strategyTypes.value.length}, 总模板数: ${totalTemplateCount}`)
  } catch (error) {
    console.error('❌ 策略模板API调用失败:', error)
    throw error // 不再使用假数据，直接抛出错误
  }
}

const openStrategyConfigDialog = async () => {
  console.log('🚀 打开策略配置对话框，开始真实API测试')

  // 清空之前的数据，确保使用的是真实API数据
  strategyTypes.value = []
  strategyTemplatesByType.value = {}

  try {
    console.log('🚀 开始真实API调用测试')

    // 1. 测试策略类型API
    console.log('🚀 步骤1: 调用策略类型API')
    await loadStrategyTypes()

    if (strategyTypes.value.length === 0) {
      throw new Error('策略类型API调用失败或返回空数据')
    }

    console.log('✅ 步骤1成功: 策略类型API调用成功，获得', strategyTypes.value.length, '个策略类型')

    // 2. 测试策略模板API
    console.log('🚀 步骤2: 调用策略模板API')
    await loadStrategyTemplates()

    const templateCount = Object.keys(strategyTemplatesByType.value).length
    if (templateCount === 0) {
      throw new Error('策略模板API调用失败或返回空数据')
    }

    console.log('✅ 步骤2成功: 策略模板API调用成功，获得', templateCount, '种策略类型的模板')
    console.log('✅ 🎉 所有API调用测试成功！这是真实的后端数据！')

    strategyConfigVisible.value = true

  } catch (error) {
    console.error('❌ API调用失败:', error.message)
    ElMessage.error('策略API调用失败: ' + error.message + '。请检查后端服务是否启动，数据库是否有策略数据。')

    // 不再使用假数据，直接返回
    return
  }
}

const handleStrategyEnabledChange = (strategyType) => {
  console.log('策略启用状态变更:', strategyType, strategyEnabled.value[strategyType])
  // 如果启用了策略但没有选择模板，自动选择默认模板
  if (strategyEnabled.value[strategyType] && !selectedStrategyTemplates.value[strategyType]) {
    const templates = strategyTemplatesByType.value[strategyType]
    if (templates && templates.length > 0) {
      const defaultTemplate = templates.find(t => t.isDefault === '1') || templates[0]
      selectedStrategyTemplates.value[strategyType] = defaultTemplate.id
    }
  }
}

const handleStrategyTemplateChange = (strategyType) => {
  console.log('策略模板变更:', strategyType, selectedStrategyTemplates.value[strategyType])
}

const confirmStrategyConfig = () => {
  console.log('确认策略配置')
  console.log('策略启用状态:', strategyEnabled.value)
  console.log('当前选中的策略模板:', selectedStrategyTemplates.value)

  // 构建选中的策略列表（只包含启用的策略）
  selectedStrategies.value = []
  for (const [strategyType, isEnabled] of Object.entries(strategyEnabled.value)) {
    console.log(`处理策略类型: ${strategyType}, 是否启用: ${isEnabled}`)
    if (isEnabled) {
      const templateId = selectedStrategyTemplates.value[strategyType]
      if (templateId) {
        const template = strategyTemplatesByType.value[strategyType]?.find(t => t.id === templateId)
        console.log(`找到的模板:`, template)
        if (template) {
          selectedStrategies.value.push({
            id: template.id,
            name: template.name,
            strategyType: strategyType,
            templateId: templateId
          })
        }
      }
    }
  }

  console.log('最终选中的策略列表:', selectedStrategies.value)
  strategyConfigVisible.value = false

  if (selectedStrategies.value.length > 0) {
    ElMessage.success(`策略配置已保存，共选择了 ${selectedStrategies.value.length} 个策略`)
  } else {
    ElMessage.info('未选择任何策略，将使用系统默认配置')
  }
}

// 生命周期
onMounted(() => {
  getDocumentList()
  getKnowledgeBaseList()
  // 不再自动初始化假数据，只有API调用成功才有数据
  console.log('🚀 页面加载完成，准备测试真实API调用')
})

// 文件夹相关方法已删除
</script>

<style scoped>
.knowledge-sidebar {
  background: #f5f5f5;
  border-radius: 4px;
  padding: 16px;
  height: calc(100vh - 200px);
  overflow-y: auto;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.knowledge-content {
  padding: 0 16px;
}

.search-bar {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.toolbar {
  margin-bottom: 16px;
  display: flex;
  gap: 8px;
}

/* 文件夹树相关样式已删除 */

.document-list {
  background: white;
  border-radius: 4px;
  padding: 16px;
}

.selected-documents {
  min-height: 60px;
  padding: 8px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
}

.no-documents {
  color: #999;
  font-size: 14px;
  text-align: center;
  padding: 20px 0;
}

.selected-documents .el-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

/* 知识库相关样式 */
.knowledge-base-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.knowledge-base-list {
  max-height: 300px;
  overflow-y: auto;
}

.knowledge-base-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.knowledge-base-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.knowledge-base-item.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.kb-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.kb-icon {
  font-size: 18px;
  color: #409eff;
  margin-right: 8px;
}

.kb-details {
  flex: 1;
}

.kb-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.kb-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.kb-doc-count {
  font-size: 12px;
  color: #909399;
}

.kb-actions {
  display: flex;
  gap: 4px;
}

.empty-state {
  text-align: center;
  padding: 20px;
}

/* 策略配置相关样式 */
.strategy-config-section {
  min-height: 60px;
  padding: 12px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
}

.strategy-config-button {
  margin-bottom: 8px;
  white-space: nowrap;
}

.selected-strategies {
  margin-top: 8px;
}

.no-strategies {
  color: #999;
  font-size: 14px;
  text-align: center;
  padding: 20px 0;
}

.strategy-config-container {
  max-height: 500px;
  overflow-y: auto;
}

.strategy-type-content {
  padding: 16px 0;
}

.strategy-type-description {
  margin-bottom: 16px;
}

.strategy-templates {
  max-height: 300px;
  overflow-y: auto;
}

.strategy-template-radio {
  display: block;
  margin-bottom: 12px;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.strategy-template-radio:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.strategy-template-radio.is-checked {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.template-info {
  margin-left: 8px;
}

.template-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.template-description {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.default-strategy-info {
  padding: 12px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  display: flex;
  align-items: center;
  margin-top: 8px;
}

/* 文件夹部分样式已删除 */
</style>
