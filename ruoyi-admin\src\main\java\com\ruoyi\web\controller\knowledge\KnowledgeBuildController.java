package com.ruoyi.web.controller.knowledge;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.knowledge.domain.KnowledgeDocument;
import com.ruoyi.knowledge.domain.KnowledgeFolder;
import com.ruoyi.knowledge.service.IKnowledgeDocumentService;
import com.ruoyi.knowledge.service.IKnowledgeFolderService;
import com.ruoyi.knowledge.service.IKnowledgeRagService;
import com.ruoyi.knowledge.service.IKnowledgeBaseService;
import com.ruoyi.knowledge.service.IKnowledgeBaseStrategyConfigService;
import com.ruoyi.knowledge.domain.KnowledgeBaseStrategyConfig;
import com.ruoyi.knowledge.domain.KnowledgeBase;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 知识库构建Controller
 * 
 * <AUTHOR>
 * @date 2024-07-10
 */
@RestController
@RequestMapping("/knowledge/build")
public class KnowledgeBuildController extends BaseController {
    @Autowired
    private IKnowledgeDocumentService knowledgeDocumentService;

    @Autowired
    private IKnowledgeFolderService knowledgeFolderService;

    @Autowired
    private IKnowledgeRagService knowledgeRagService;

    @Autowired
    private IKnowledgeBaseService knowledgeBaseService;

    @Autowired
    private IKnowledgeBaseStrategyConfigService knowledgeBaseStrategyConfigService;

    /**
     * 查询知识库列表
     */
    @PreAuthorize("@ss.hasPermi('knowledge:build:query')")
    @GetMapping("/knowledge-base/list")
    public TableDataInfo knowledgeBaseList(KnowledgeBase knowledgeBase) {
        startPage();
        List<KnowledgeBase> list = knowledgeBaseService.selectKnowledgeBaseList(knowledgeBase);
        return getDataTable(list);
    }

    /**
     * 获取知识库详细信息
     */
    @PreAuthorize("@ss.hasPermi('knowledge:build:query')")
    @GetMapping("/knowledge-base/{id}")
    public AjaxResult getKnowledgeBaseInfo(@PathVariable("id") Long id) {
        return success(knowledgeBaseService.selectKnowledgeBaseById(id));
    }

    /**
     * 删除知识库
     */
    @PreAuthorize("@ss.hasPermi('knowledge:build:remove')")
    @Log(title = "删除知识库", businessType = BusinessType.DELETE)
    @DeleteMapping("/knowledge-base/{ids}")
    public AjaxResult removeKnowledgeBase(@PathVariable Long[] ids) {
        return toAjax(knowledgeBaseService.deleteKnowledgeBaseByIds(ids));
    }

    /**
     * 查询知识库文档列表
     */
    @PreAuthorize("@ss.hasPermi('knowledge:build:query')")
    @GetMapping("/document/list")
    public TableDataInfo documentList(KnowledgeDocument knowledgeDocument) {
        startPage();
        List<KnowledgeDocument> list = knowledgeDocumentService.selectKnowledgeDocumentList(knowledgeDocument);
        return getDataTable(list);
    }

    /**
     * 根据文件夹ID查询文档列表
     */
    @PreAuthorize("@ss.hasPermi('knowledge:build:query')")
    @GetMapping("/document/folder/{folderId}")
    public AjaxResult getDocumentsByFolder(@PathVariable("folderId") Long folderId) {
        List<KnowledgeDocument> list = knowledgeDocumentService.selectKnowledgeDocumentByFolderId(folderId);
        return success(list);
    }

    /**
     * 搜索文档
     */
    @PreAuthorize("@ss.hasPermi('knowledge:build:query')")
    @GetMapping("/document/search")
    public AjaxResult searchDocuments(@RequestParam("keyword") String keyword) {
        List<KnowledgeDocument> list = knowledgeDocumentService.searchKnowledgeDocumentByKeyword(keyword);
        return success(list);
    }

    /**
     * 新增知识库文档
     */
    @PreAuthorize("@ss.hasPermi('knowledge:build:add')")
    @Log(title = "知识库文档", businessType = BusinessType.INSERT)
    @PostMapping("/document")
    public AjaxResult addDocument(@RequestBody KnowledgeDocument knowledgeDocument) {
        return toAjax(knowledgeDocumentService.insertKnowledgeDocument(knowledgeDocument));
    }

    /**
     * 删除知识库文档
     */
    @PreAuthorize("@ss.hasPermi('knowledge:build:remove')")
    @Log(title = "知识库文档", businessType = BusinessType.DELETE)
    @DeleteMapping("/document/{ids}")
    public AjaxResult removeDocuments(@PathVariable Long[] ids) {
        return toAjax(knowledgeDocumentService.deleteKnowledgeDocumentByIds(ids));
    }

    /**
     * 上传文档
     */
    @PreAuthorize("@ss.hasPermi('knowledge:build:upload')")
    @Log(title = "文档上传", businessType = BusinessType.INSERT)
    @PostMapping("/document/upload")
    public AjaxResult uploadDocument(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "folderId", required = false) Long folderId,
            @RequestParam(value = "knowledgeBaseId", required = false) Long knowledgeBaseId) {
        try {
            KnowledgeDocument document = knowledgeDocumentService.uploadDocument(file, folderId, knowledgeBaseId);
            return success(document);
        } catch (Exception e) {
            logger.error("文档上传失败", e);
            return error("上传失败: " + e.getMessage());
        }
    }

    /**
     * 批量上传文档
     */
    @PreAuthorize("@ss.hasPermi('knowledge:build:upload')")
    @Log(title = "批量文档上传", businessType = BusinessType.INSERT)
    @PostMapping("/document/batch-upload")
    public AjaxResult batchUploadDocuments(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam(value = "folderId", required = false) Long folderId,
            @RequestParam(value = "knowledgeBaseId", required = false) Long knowledgeBaseId) {
        try {
            List<KnowledgeDocument> documents = knowledgeDocumentService.batchUploadDocuments(files, folderId,
                    knowledgeBaseId);
            return success(documents);
        } catch (Exception e) {
            logger.error("批量文档上传失败", e);
            return error("批量上传失败: " + e.getMessage());
        }
    }

    /**
     * 处理文档
     */
    @PreAuthorize("@ss.hasPermi('knowledge:build:edit')")
    @Log(title = "文档处理", businessType = BusinessType.UPDATE)
    @PostMapping("/document/process/{id}")
    public AjaxResult processDocument(@PathVariable("id") Long id) {
        boolean result = knowledgeDocumentService.processDocument(id);
        return result ? success("文档处理成功") : error("文档处理失败");
    }

    /**
     * 导出知识库文档
     */
    @PreAuthorize("@ss.hasPermi('knowledge:build:export')")
    @Log(title = "知识库文档", businessType = BusinessType.EXPORT)
    @PostMapping("/document/export")
    public void exportDocuments(HttpServletResponse response, KnowledgeDocument knowledgeDocument) {
        List<KnowledgeDocument> list = knowledgeDocumentService.selectKnowledgeDocumentList(knowledgeDocument);
        ExcelUtil<KnowledgeDocument> util = new ExcelUtil<KnowledgeDocument>(KnowledgeDocument.class);
        util.exportExcel(response, list, "知识库文档数据");
    }

    /**
     * 创建知识库
     * 将选中的文档上传到向量数据库
     */
    @PreAuthorize("@ss.hasPermi('knowledge:build:add')")
    @Log(title = "创建知识库", businessType = BusinessType.INSERT)
    @PostMapping("/knowledge-base/create")
    public AjaxResult createKnowledgeBase(@RequestBody CreateKnowledgeBaseRequest request) {
        try {
            logger.info("创建知识库请求: {}", request);

            // 验证参数
            if (request.getName() == null || request.getName().trim().isEmpty()) {
                return error("知识库名称不能为空");
            }

            if (request.getDocumentIds() == null || request.getDocumentIds().isEmpty()) {
                return error("请选择要添加到知识库的文档");
            }

            // 调用RAG服务创建知识库并获取知识库ID
            Long knowledgeBaseId = knowledgeRagService.createKnowledgeBaseWithId(
                    request.getName().trim(),
                    request.getDescription() != null ? request.getDescription().trim() : "",
                    request.getDocumentIds());

            if (knowledgeBaseId != null) {
                // 保存策略配置
                if (request.getStrategyConfigs() != null && !request.getStrategyConfigs().isEmpty()) {
                    logger.info("开始保存知识库策略配置，知识库ID: {}, 策略数量: {}",
                            knowledgeBaseId, request.getStrategyConfigs().size());

                    for (StrategyConfigRequest strategyConfig : request.getStrategyConfigs()) {
                        try {
                            KnowledgeBaseStrategyConfig config = new KnowledgeBaseStrategyConfig();
                            config.setKnowledgeBaseId(knowledgeBaseId);
                            config.setStrategyTemplateId(strategyConfig.getStrategyTemplateId());
                            config.setStrategyType(strategyConfig.getStrategyType());
                            config.setConfigJson(strategyConfig.getConfigJson());
                            config.setIsEnabled(strategyConfig.getEnabled() != null && strategyConfig.getEnabled() ? "1" : "0");
                            config.setExecutionOrder(strategyConfig.getExecutionOrder());

                            int saveResult = knowledgeBaseStrategyConfigService.insertKnowledgeBaseStrategyConfig(config);
                            if (saveResult > 0) {
                                logger.info("策略配置保存成功: 知识库ID={}, 策略类型={}, 模板ID={}",
                                        knowledgeBaseId, strategyConfig.getStrategyType(), strategyConfig.getStrategyTemplateId());
                            } else {
                                logger.error("策略配置保存失败: 知识库ID={}, 策略类型={}",
                                        knowledgeBaseId, strategyConfig.getStrategyType());
                            }
                        } catch (Exception e) {
                            logger.error("保存策略配置时出错: {}", e.getMessage(), e);
                        }
                    }

                    logger.info("知识库策略配置保存完成");
                }

                return success("知识库创建成功");
            } else {
                return error("知识库创建失败");
            }

        } catch (Exception e) {
            logger.error("创建知识库失败", e);
            return error("创建知识库失败: " + e.getMessage());
        }
    }

    /**
     * 查询知识库的策略配置
     */
    @PreAuthorize("@ss.hasPermi('knowledge:build:query')")
    @GetMapping("/knowledge-base/{id}/strategies")
    public AjaxResult getKnowledgeBaseStrategies(@PathVariable("id") Long knowledgeBaseId) {
        try {
            logger.info("查询知识库策略配置，知识库ID: {}", knowledgeBaseId);

            KnowledgeBaseStrategyConfig queryConfig = new KnowledgeBaseStrategyConfig();
            queryConfig.setKnowledgeBaseId(knowledgeBaseId);

            List<KnowledgeBaseStrategyConfig> strategies = knowledgeBaseStrategyConfigService
                    .selectKnowledgeBaseStrategyConfigList(queryConfig);

            logger.info("找到 {} 个策略配置", strategies.size());
            return success(strategies);

        } catch (Exception e) {
            logger.error("查询知识库策略配置失败", e);
            return error("查询策略配置失败: " + e.getMessage());
        }
    }

    /**
     * 创建知识库请求对象
     */
    public static class CreateKnowledgeBaseRequest {
        private String name;
        private String description;
        private List<Long> documentIds;
        private List<StrategyConfigRequest> strategyConfigs;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public List<Long> getDocumentIds() {
            return documentIds;
        }

        public void setDocumentIds(List<Long> documentIds) {
            this.documentIds = documentIds;
        }

        public List<StrategyConfigRequest> getStrategyConfigs() {
            return strategyConfigs;
        }

        public void setStrategyConfigs(List<StrategyConfigRequest> strategyConfigs) {
            this.strategyConfigs = strategyConfigs;
        }

        @Override
        public String toString() {
            return "CreateKnowledgeBaseRequest{" +
                    "name='" + name + '\'' +
                    ", description='" + description + '\'' +
                    ", documentIds=" + documentIds +
                    ", strategyConfigs=" + strategyConfigs +
                    '}';
        }
    }

    /**
     * 策略配置请求对象
     */
    public static class StrategyConfigRequest {
        private Long strategyTemplateId;
        private String strategyType;
        private String configJson;
        private Boolean enabled;
        private Integer executionOrder;

        public Long getStrategyTemplateId() {
            return strategyTemplateId;
        }

        public void setStrategyTemplateId(Long strategyTemplateId) {
            this.strategyTemplateId = strategyTemplateId;
        }

        public String getStrategyType() {
            return strategyType;
        }

        public void setStrategyType(String strategyType) {
            this.strategyType = strategyType;
        }

        public String getConfigJson() {
            return configJson;
        }

        public void setConfigJson(String configJson) {
            this.configJson = configJson;
        }

        public Boolean getEnabled() {
            return enabled;
        }

        public void setEnabled(Boolean enabled) {
            this.enabled = enabled;
        }

        public Integer getExecutionOrder() {
            return executionOrder;
        }

        public void setExecutionOrder(Integer executionOrder) {
            this.executionOrder = executionOrder;
        }

        @Override
        public String toString() {
            return "StrategyConfigRequest{" +
                    "strategyTemplateId=" + strategyTemplateId +
                    ", strategyType='" + strategyType + '\'' +
                    ", configJson='" + configJson + '\'' +
                    ", enabled=" + enabled +
                    ", executionOrder=" + executionOrder +
                    '}';
        }
    }

    // ==================== 文件夹相关接口 ====================

    /**
     * 查询知识库文件夹树结构
     */
    @PreAuthorize("@ss.hasPermi('knowledge:build:query')")
    @GetMapping("/folder/tree")
    public AjaxResult getFolderTree(@RequestParam(value = "knowledgeBaseId", required = false) Long knowledgeBaseId) {
        List<KnowledgeFolder> folderTree = knowledgeFolderService.selectKnowledgeFolderTree(knowledgeBaseId);
        return success(folderTree);
    }

    /**
     * 查询知识库文件夹列表
     */
    @PreAuthorize("@ss.hasPermi('knowledge:build:query')")
    @GetMapping("/folder/list")
    public AjaxResult folderList(KnowledgeFolder knowledgeFolder) {
        List<KnowledgeFolder> list = knowledgeFolderService.selectKnowledgeFolderList(knowledgeFolder);
        return success(list);
    }

    /**
     * 获取知识库文件夹详细信息
     */
    @PreAuthorize("@ss.hasPermi('knowledge:build:query')")
    @GetMapping("/folder/{id}")
    public AjaxResult getFolderInfo(@PathVariable("id") Long id) {
        return success(knowledgeFolderService.selectKnowledgeFolderById(id));
    }

    /**
     * 新增知识库文件夹
     */
    @PreAuthorize("@ss.hasPermi('knowledge:build:add')")
    @Log(title = "知识库文件夹", businessType = BusinessType.INSERT)
    @PostMapping("/folder")
    public AjaxResult addFolder(@RequestBody KnowledgeFolder knowledgeFolder) {
        if (!knowledgeFolderService.checkFolderNameUnique(knowledgeFolder)) {
            return error("新增文件夹'" + knowledgeFolder.getName() + "'失败，文件夹名称已存在");
        }
        return toAjax(knowledgeFolderService.insertKnowledgeFolder(knowledgeFolder));
    }

    /**
     * 修改知识库文件夹
     */
    @PreAuthorize("@ss.hasPermi('knowledge:build:edit')")
    @Log(title = "知识库文件夹", businessType = BusinessType.UPDATE)
    @PutMapping("/folder")
    public AjaxResult editFolder(@RequestBody KnowledgeFolder knowledgeFolder) {
        if (!knowledgeFolderService.checkFolderNameUnique(knowledgeFolder)) {
            return error("修改文件夹'" + knowledgeFolder.getName() + "'失败，文件夹名称已存在");
        }
        return toAjax(knowledgeFolderService.updateKnowledgeFolder(knowledgeFolder));
    }

    /**
     * 删除知识库文件夹
     */
    @PreAuthorize("@ss.hasPermi('knowledge:build:remove')")
    @Log(title = "知识库文件夹", businessType = BusinessType.DELETE)
    @DeleteMapping("/folder/{ids}")
    public AjaxResult removeFolders(@PathVariable Long[] ids) {
        return toAjax(knowledgeFolderService.deleteKnowledgeFolderByIds(ids));
    }
}
