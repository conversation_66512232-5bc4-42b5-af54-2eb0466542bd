-- 测试知识库策略配置持久化保存
-- 用于验证策略配置是否正确保存到数据库

-- 1. 查看所有知识库
SELECT 
    id,
    name,
    description,
    document_count,
    status,
    create_time
FROM knowledge_base 
ORDER BY create_time DESC 
LIMIT 10;

-- 2. 查看最新创建的知识库的策略配置
SELECT 
    kbsc.id,
    kbsc.knowledge_base_id,
    kb.name as knowledge_base_name,
    kbsc.strategy_template_id,
    kst.name as strategy_template_name,
    kbsc.strategy_type,
    kbsc.config_json,
    kbsc.is_enabled,
    kbsc.execution_order,
    kbsc.create_time
FROM knowledge_base_strategy_config kbsc
LEFT JOIN knowledge_base kb ON kbsc.knowledge_base_id = kb.id
LEFT JOIN knowledge_strategy_template kst ON kbsc.strategy_template_id = kst.id
ORDER BY kbsc.knowledge_base_id DESC, kbsc.execution_order ASC
LIMIT 20;

-- 3. 查看特定知识库的策略配置（替换 [KNOWLEDGE_BASE_ID] 为实际的知识库ID）
-- SELECT 
--     kbsc.id,
--     kbsc.strategy_template_id,
--     kst.name as strategy_template_name,
--     kbsc.strategy_type,
--     kbsc.config_json,
--     kbsc.is_enabled,
--     kbsc.execution_order,
--     kbsc.create_time
-- FROM knowledge_base_strategy_config kbsc
-- LEFT JOIN knowledge_strategy_template kst ON kbsc.strategy_template_id = kst.id
-- WHERE kbsc.knowledge_base_id = [KNOWLEDGE_BASE_ID]
-- ORDER BY kbsc.execution_order ASC;

-- 4. 统计各知识库的策略配置数量
SELECT 
    kb.id,
    kb.name,
    COUNT(kbsc.id) as strategy_count,
    kb.create_time
FROM knowledge_base kb
LEFT JOIN knowledge_base_strategy_config kbsc ON kb.id = kbsc.knowledge_base_id
GROUP BY kb.id, kb.name, kb.create_time
ORDER BY kb.create_time DESC;

-- 5. 查看所有可用的策略模板
SELECT 
    id,
    name,
    description,
    strategy_type,
    is_default,
    is_enabled,
    sort_order
FROM knowledge_strategy_template
WHERE is_enabled = '1'
ORDER BY strategy_type, sort_order;

-- 6. 验证策略配置的完整性
-- 检查是否有策略配置引用了不存在的模板
SELECT 
    kbsc.id,
    kbsc.knowledge_base_id,
    kbsc.strategy_template_id,
    kbsc.strategy_type,
    CASE 
        WHEN kst.id IS NULL THEN '模板不存在'
        WHEN kst.is_enabled = '0' THEN '模板已禁用'
        ELSE '正常'
    END as status
FROM knowledge_base_strategy_config kbsc
LEFT JOIN knowledge_strategy_template kst ON kbsc.strategy_template_id = kst.id
WHERE kst.id IS NULL OR kst.is_enabled = '0';

-- 7. 查看策略执行日志（如果有的话）
-- SELECT
--     id,
--     knowledge_base_id,
--     strategy_template_id,
--     strategy_type,
--     execution_status,
--     start_time,
--     end_time,
--     execution_time_ms,
--     error_message
-- FROM knowledge_strategy_execution_log
-- ORDER BY start_time DESC
-- LIMIT 10;

-- 8. 查看知识库文档的标签信息
SELECT
    kd.id,
    kd.name as document_name,
    kd.knowledge_base_id,
    kb.name as knowledge_base_name,
    kd.tags,
    kd.create_time
FROM knowledge_document kd
LEFT JOIN knowledge_base kb ON kd.knowledge_base_id = kb.id
WHERE kd.tags IS NOT NULL AND kd.tags != ''
ORDER BY kd.knowledge_base_id, kd.create_time DESC;

-- 9. 统计各知识库的标签分布
SELECT
    kb.id as knowledge_base_id,
    kb.name as knowledge_base_name,
    COUNT(CASE WHEN kd.tags IS NOT NULL AND kd.tags != '' THEN 1 END) as documents_with_tags,
    COUNT(kd.id) as total_documents,
    ROUND(COUNT(CASE WHEN kd.tags IS NOT NULL AND kd.tags != '' THEN 1 END) * 100.0 / COUNT(kd.id), 2) as tag_coverage_percent
FROM knowledge_base kb
LEFT JOIN knowledge_document kd ON kb.id = kd.knowledge_base_id
GROUP BY kb.id, kb.name
ORDER BY tag_coverage_percent DESC;

-- 10. 查看特定知识库的标签详情（替换 [KNOWLEDGE_BASE_ID] 为实际的知识库ID）
-- SELECT
--     kd.name as document_name,
--     kd.tags,
--     kd.type as document_type,
--     kd.format as document_format,
--     kd.create_time
-- FROM knowledge_document kd
-- WHERE kd.knowledge_base_id = [KNOWLEDGE_BASE_ID]
--   AND kd.tags IS NOT NULL
--   AND kd.tags != ''
-- ORDER BY kd.create_time DESC;
